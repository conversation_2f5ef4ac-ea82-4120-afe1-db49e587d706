<?php

namespace app\lib\client\driver;

use app\lib\client\ClientProxy;
use app\lib\client\Driver;
use app\model\Book;
use app\model\KeyPair;
use app\model\Release;
use GuzzleHttp\Client;
use Spatie\Url\Url;
use think\ChunkUpload\Client as UploadClient;
use think\File;
use think\helper\Arr;

class Topthink extends Driver
{

    public function getRemotePath(Book $book)
    {
        if ($book->ssh_url) {
            return $book->ssh_url;
        }
        $url = Url::fromString($book->git_url);
        return (string) $url->withUserInfo('jwt_token', $this->user->token);
    }

    public function getSshCommand(Book $book)
    {
        if ($book->ssh_url) {
            return parent::getSshCommand($book);
        }
        return null;
    }

    public function checkPublicKey(KeyPair $keyPair)
    {
        $this->client->get("me/key/{$keyPair->key_id}");
    }

    public function createPublicKey($publicKey)
    {
        $res = $this->client->post('me/key', [
            'form_params' => [
                'key'   => $publicKey,
                'title' => 'TopWrite',
            ],
        ]);
        return $res['id'];
    }

    public function release(Release $release, Book $book, $type, File $file)
    {
        $client = new UploadClient("{$this->user->app->url}/api/me/release", 'POST', [
            'Authorization' => "Token {$this->user->token}",
        ]);

        $client->upload($file, [
            'id'   => $book->openid,
            'sha'  => $release->sha,
            'type' => $type,
        ]);
    }

    public function getPresetPlugins(Book $book)
    {
        $url = Url::fromString($book->url);

        $plugins = [
            'knowledge'  => [
                'host' => (string) $url->withPath('/'),
            ],
            'components' => [],
        ];

        return $plugins;
    }

    public function getAiClient(Book $book)
    {
        $plan       = Arr::get($book->metadata, 'plan');

        //抢先用户
        $preemptive = Arr::get($book->metadata, 'preemptive');

        if (!empty($plan) && $preemptive) {
            $url    = Url::fromString($book->url);
            $client = new \think\ai\Client($this->user->token);
            $client->setEndpoint((string) $url->withPath('/api/ai/'));
            return $client;
        }
        return null;
    }

    public function getPoweredBy(Book $book)
    {
        if (!Arr::get($book->metadata, 'powered_by', true)) {
            return false;
        }

        return [
            'name' => '顶想云',
            'link' => 'https://www.topthink.com',
        ];
    }

    public function getAssetType(Book $book)
    {
        return 'cdn';
    }

    public function getReleaseTypes(Book $book)
    {
        return ['json', ...Arr::get($book->metadata, 'pack_types', [])];
    }

    public function getReleaseTimeout(Book $book)
    {
        $plan = Arr::get($book->metadata, 'plan');

        return match ($plan) {
            'enterprise' => 60 * 20,
            'team' => 60 * 10,
            default => 60 * 5,
        };
    }

    public function getLfsFiles(Book $book)
    {
        return $this->client->get("book/{$book->openid}/lfs-files");
    }

    protected function makeClient()
    {
        $client = new Client([
            'base_uri' => "{$this->app->url}/api/",
            'headers'  => [
                'Accept'        => 'application/json',
                'Authorization' => "Token {$this->user->token}",
            ],
        ]);

        return new ClientProxy($client);
    }
}
