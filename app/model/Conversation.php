<?php

namespace app\model;

use think\Model;
use think\model\concern\SoftDelete;

/**
 * Class app\model\Conversation
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $id
 * @property int $space_id
 * @property string $delete_time
 * @property string $title
 * @property-read \app\model\Message[] $messages
 * @property-read \app\model\Space $space
 * @method static \think\db\Query onlyTrashed()
 * @method static \think\db\Query withTrashed()
 */
class Conversation extends Model
{
    use SoftDelete;

    public function space()
    {
        return $this->belongsTo(Space::class);
    }

    public function messages()
    {
        return $this->hasMany(Message::class);
    }
}
