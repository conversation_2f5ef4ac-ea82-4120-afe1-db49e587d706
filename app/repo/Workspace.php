<?php

namespace app\repo;

use app\lib\Filesystem;
use app\lib\Ripgrep;
use app\model\Space;
use app\repo\exception\FileScopeException;
use app\repo\parser\LfsFilesParser;
use Closure;
use Diff\Differ\MapDiffer;
use Exception;
use Intervention\Image\Exception\RuntimeException;
use InvalidArgumentException;
use JsonException;
use Swoole\Event;
use Swoole\Timer;
use Symfony\Component\Filesystem\Path;
use Symfony\Component\Finder\Finder;
use Symfony\Component\Process\Process;
use think\facade\Log;
use think\helper\Arr;
use think\helper\Str;
use think\swoole\coroutine\Context;
use topthink\git\Commit;
use topthink\git\exception\ProcessException;
use topthink\git\parser\ResetParser;
use topthink\git\Repository;
use topthink\git\Status;
use function array_values;

class Workspace
{
    protected Filesystem $fs;

    protected Ripgrep $rg;

    protected $listeners = [];

    protected Status $status;

    /** @var string */
    protected $current = null;

    protected $currentFile;

    protected bool $locked  = false;
    protected bool $syncing = false;

    protected $inotify;

    protected $descriptors = [];

    protected $root;
    protected $rootDir;
    protected $gitDir;
    protected $workingDir;
    protected $configFile;

    protected $changeTimer = 0;
    protected $changes     = [];

    public function __construct(protected Space $space, protected Repository $repo)
    {
        $this->fs = new Filesystem();

        $this->gitDir     = $this->repo->getGitDir();
        $this->workingDir = $this->repo->getWorkingDir();

        $this->currentFile = Path::join($this->gitDir, 'CURRENT');
        $this->current     = file_exists($this->currentFile) ? file_get_contents($this->currentFile) : null;

        $this->configFile = $this->resolveFullPath(Config::FILE);
        $this->root       = $this->getConfig()->getValue('root', '');
        $this->rootDir    = $this->resolveFullPath($this->root);

        $this->rg = new Ripgrep($this->rootDir);
    }

    public function init()
    {
        $lastRelease = $this->space->book->getLastRelease();

        if ($lastRelease) {
            $this->emit('workspace.release', $lastRelease->status_text);
        }

        $this->emit('tree.change', $this->getTreeEntries());

        //清理lock文件
        Process::fromShellCommandline('rm *.lock', $this->gitDir)->run();

        //更新远程地址
        $remotePath = $this->space->user->getClient()->getRemotePath($this->space->book);
        $this->repo->run('remote', ['set-url', 'origin', $remotePath]);

        $sshCommand = $this->space->user->getClient()->getSshCommand($this->space->book);
        if ($sshCommand) {
            $this->repo->run('config', ['core.sshCommand', $sshCommand]);
        }

        //更新author
        $this->repo->run('config', ['user.name', $this->space->user->name]);
        $this->repo->run('config', ['user.email', $this->space->user->email]);

        $this->emit('workspace.syncing', true);

        //拉取
        $branch = $this->getBranch();
        $this->repo->run('fetch', ['origin', $branch], ['callback' => [$this, 'emitSyncMessage']]);

        $this->updateStatus();

        [$ahead, $behind] = $this->status->branch['ab'];

        //合并
        if ($behind > 0 && $this->status->isClean()) {
            try {
                $result = $this->repo->run('merge', args: ["origin/{$branch}", '--ff-only'], options: ['callback' => [$this, 'emitSyncMessage']]);

                if (!$result) {
                    //TODO 记录pull失败的日志
                    //检查是否分叉
                    //TODO 改进、备份后reset
                    $output = $this->repo->run('status');
                    if (Str::contains($output, 'have diverged')) {
                        $this->repo->run('reset', ['--hard', "origin/{$branch}"]);
                    }
                }
            } catch (ProcessException $e) {
                $output = $e->getErrorOutput();

                //仓库历史有变动
                if (Str::contains($output, 'refusing to merge unrelated histories')) {
                    $this->repo->run('reset', ['--hard', "origin/{$branch}"]);
                }
            }

            $this->updateStatus();
        }

        //推送
        if ($behind == 0 && $ahead > 0) {
            $this->repo->push($branch, options: ['callback' => [$this, 'emitSyncMessage']]);
            $this->updateStatus();
        }

        $this->emit('sync.message', '');
        $this->emit('workspace.syncing', false);

        //lfs
        if ($this->space->supportLfs()) {
            $assetDir = Path::makeRelative($this->getAssetPath(), $this->workingDir);

            $this->repo->run('lfs', ['install', '--local', '--force']);
            $this->repo->run('lfs', ['track', "{$assetDir}/**"]);

            //检查lfs
            $this->checkLfsFiles();
        }

        //gc
        if (random_int(1, 100) <= 10) {
            $this->repo->run('gc');
        }
    }

    protected function checkLfsFiles()
    {
        $lfsFiles = $this->space->getRemoteLfsFiles();

        $output = $this->repo->run('lfs', ['ls-files', '-l']);

        $parser = new LfsFilesParser();

        $files = $parser->parse($output);

        $notUploaded = array_filter($files, function ($file) use ($lfsFiles) {
            return !in_array($file['id'], $lfsFiles);
        });

        if (!empty($notUploaded)) {
            $oid = array_map(function ($file) {
                return $file['id'];
            }, $notUploaded);

            $this->repo->run('lfs', ['push', '--object-id', 'origin', ...$oid]);
        }
    }

    public function emitSyncMessage($buffer)
    {
        $message = Arr::last(preg_split("/[\r\n]+/s", trim($buffer)));

        $this->emit('sync.message', $message);
    }

    public function getTreeEntries()
    {
        $tree = $this->repo->getHeadCommit()->getTree();

        if (!empty($this->root)) {
            try {
                $configEntry = $tree->getEntry(Config::FILE);
            } catch (Exception) {
                $configEntry = null;
            }
            try {
                foreach (explode('/', $this->root) as $name) {
                    $entry = $tree->getEntry($name);
                    $tree  = $entry->getTree();
                }
            } catch (Exception) {
                $tree = null;
            }

            $entries = $tree ? $tree->getEntries() : [];
            if ($configEntry) {
                $entries[Config::FILE] = $configEntry;
            }
        } else {
            $entries = $tree->getEntries();
        }

        return $entries;
    }

    public function hasDir($root = '')
    {
        $dir = $this->resolveLocalPath($root);

        return $this->fs->isDir($dir);
    }

    public function readDir($root = ''): array
    {
        $dir = $this->resolveLocalPath($root);

        $files = [];

        if ($this->fs->isDir($dir)) {
            $finder = new Finder();
            $finder->in($dir)
                ->sortByType()
                ->depth(0);

            foreach ($finder as $file) {
                $files[] = [
                    'pathname' => Path::join($root, $file->getRelativePathname()),
                    'filename' => $file->getFilename(),
                    'type'     => $file->getType(),
                ];
            }
        }

        if ($root == '' && !empty($this->root) && file_exists($this->configFile)) {
            $files[] = [
                'pathname' => Config::FILE,
                'filename' => Config::FILE,
                'type'     => 'file',
            ];
        }

        return $files;
    }

    public function hasFile($filename)
    {
        $realpath = $this->resolveLocalPath($filename);

        return $this->fs->isFile($realpath);
    }

    public function readFile($filename, $force = false)
    {
        $realpath = $this->resolveLocalPath($filename);

        if (!$this->fs->isFile($realpath)) {
            if ($force) {
                throw new RuntimeException('file not exist');
            }
            return null;
        }

        return file_get_contents($realpath);
    }

    public function writeFile($filename, $content)
    {
        $realpath = $this->resolveLocalPath($filename);

        $this->ensureDirExists($realpath);

        file_put_contents($realpath, $content);
    }

    public function ensureDirExists($filename)
    {
        $dir = dirname($filename);

        if (!is_dir($dir)) {
            $this->fs->mkdir($dir);
        }
    }

    public function renameFile($fromPath, $toPath)
    {
        $origin = $this->resolveLocalPath($fromPath);
        $target = $this->resolveLocalPath($toPath);
        if (file_exists($origin) && !file_exists($target)) {
            $this->ensureDirExists($target);
            rename($origin, $target);
        }
    }

    public function removeFile(string $filename)
    {
        $this->fs->remove($this->resolveLocalPath($filename));
    }

    public function searchFile(string $query, $isCaseSensitive = false, $isWordMatch = false, $isRegExp = false)
    {
        return $this->rg->search($query, $isCaseSensitive, $isWordMatch, $isRegExp);
    }

    public function replaceFile($filename, $replace, $line, $offset, $length = null)
    {
        $content = $this->readFile($filename, true);
        $lines   = explode("\n", $content);

        $lines[$line - 1] = substr_replace($lines[$line - 1], $replace, $offset, $length);

        $this->writeFile($filename, implode("\n", $lines));
    }

    public function readDiff($revisions, $paths = [])
    {
        $diff = $this->getRepo()->getDiff($revisions, $paths);
        return $diff->toArray()['files'];
    }

    public function readHistory($path, $offset)
    {
        $branch = $this->getBranch();

        if (!$this->getRepo()->getReferences()->hasBranch($branch)) {
            return [];
        }

        $path = $path ? $this->resolveLocalPath($path) : null;
        $log  = $this->getRepo()->getLog($branch, $path, $offset, 15);

        $commits = [];
        /** @var Commit $commit */
        foreach ($log as $commit) {
            $commits[] = [
                'sha'     => $commit->getHash(),
                'author'  => [
                    'date'  => $commit->getAuthorDate()->format(DATE_ISO8601),
                    'email' => $commit->getAuthorEmail(),
                    'name'  => $commit->getAuthorName(),
                ],
                'message' => $commit->getSubjectMessage(),
            ];
        }
        return $commits;
    }

    public function discard($path)
    {
        $this->withLock(function () use ($path) {
            $this->repo->add(['.']);
            $raw = $this->repo->run('reset', ['--', $path]);
            //检查 un staged 文件 执行restore
            $parser = new ResetParser();
            $parser->parse($raw);
            $files = $parser->files;
            if (!empty($files)) {
                $this->repo->run('restore', array_merge(['--'], $files));
            }
            //执行git clean
            $this->repo->run('clean', ['-df']);
        });
    }

    public function commit(string $message, $rollback = false)
    {
        return $this->withLock(function () use ($rollback, $message) {
            $this->repo->add(['.']);

            $commitId = $this->repo->getHeadCommit()->getHash();

            $this->repo->run('commit', ['-m', $message]);

            $result = $this->sync(false);

            if (!$result && $rollback) {
                //版本回退
                $this->repo->run('reset', ['--soft', $commitId]);
            }

            return $result;
        });
    }

    public function sync($withLock = true)
    {
        $sync = function () use ($withLock) {
            if ($this->syncing) {
                return false;
            }

            try {
                $this->syncing = true;

                if (!$withLock) {
                    $commit  = $this->repo->getHeadCommit();
                    $parents = $commit->getParentHashes();
                    if (count($parents) > 1) {
                        $this->emit('sync.error', 'Unable to merge with multiple parents');
                        return false;
                    }
                }

                $branch = $this->getBranch();
                $this->repo->run('fetch', ['origin', $branch], ['callback' => [$this, 'emitSyncMessage']]);

                try {
                    $this->repo->run('merge', args: [
                        "origin/{$branch}",
                        '-m',
                        'Auto Merge',
                    ], options: ['callback' => [$this, 'emitSyncMessage']]);

                    $this->repo->push($branch, options: ['callback' => [$this, 'emitSyncMessage']]);
                } catch (ProcessException) {
                    //冲突
                }

                //更新treeEntries
                $this->emit('tree.change', $this->getTreeEntries());

                return true;
            } catch (ProcessException $e) {
                $this->emit('sync.error', $e->getErrorOutput());
                return false;
            } finally {
                $this->syncing = false;
                $this->emit('sync.message', '');
                $this->emit('workspace.syncing', false);
            }
        };

        if ($withLock) {
            return $this->withLock($sync);
        } else {
            return $sync();
        }
    }

    public function resolve($files)
    {
        foreach ($files as $filename => $content) {
            $this->writeFile($this->resolveRelativePath($filename), $content);
        }

        $this->commit('Resolve conflict');
    }

    public function getRepo(): Repository
    {
        return $this->repo;
    }

    public function getStatus()
    {
        return $this->status;
    }

    public function getBranch()
    {
        return $this->status?->branch['head'] ?? 'master';
    }

    public function getAssetPath($path = '')
    {
        return $this->resolveLocalPath(Path::join('.topwrite/assets', $path));
    }

    public function getConfig()
    {
        $config = new Config();

        if (file_exists($this->configFile)) {
            try {
                $values = json_decode(file_get_contents($this->configFile), true, 512, JSON_THROW_ON_ERROR);
                $config->setValues($values);
            } catch (JsonException) {
            }
        }

        return $config;
    }

    public function setConfig($data)
    {
        $text = '';
        if (!empty($data)) {
            $text = json_encode($data, JSON_ERROR_NONE | JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        }
        file_put_contents($this->configFile, $text);
    }

    public function getSummary()
    {
        try {
            $content = $this->readFile('SUMMARY.json', true);
            return json_decode($content, true, 512, JSON_THROW_ON_ERROR);
        } catch (Exception) {
            return (string) $this->readFile('SUMMARY.md');
        }
    }

    public function setSummary($summary)
    {
        if (is_string($summary)) {
            $this->writeFile('SUMMARY.md', $summary);
        } else {
            $this->removeFile('SUMMARY.md');
            $this->writeFile('SUMMARY.json', json_encode($summary, JSON_ERROR_NONE | JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        }
    }

    public function getCurrent()
    {
        return $this->current;
    }

    public function getRootDir()
    {
        return $this->rootDir;
    }

    public function setCurrent($path)
    {
        if ($path) {
            file_put_contents($this->currentFile, $path);
        } else {
            unlink($this->currentFile);
        }
        $this->current = $path;
    }

    public function on($event, callable $listener)
    {
        if ($event === null) {
            throw new InvalidArgumentException('event name must not be null');
        }

        if (!isset($this->listeners[$event])) {
            $this->listeners[$event] = [];
        }

        $this->listeners[$event][] = $listener;

        return $this;
    }

    public function emit($event, ...$arguments)
    {
        if ($event === null) {
            throw new InvalidArgumentException('event name must not be null');
        }

        if (!empty($this->listeners['*'])) {
            foreach ($this->listeners['*'] as $listener) {
                $listener($event, ...$arguments);
            }
        }

        if (!empty($this->listeners[$event])) {
            foreach ($this->listeners[$event] as $listener) {
                $listener(...$arguments);
            }
        }
    }

    public function watchFiles()
    {
        $this->status = $this->repo->getStatus();

        $paths = [$this->workingDir];

        if (file_exists($this->rootDir)) {
            $paths[] = $this->rootDir;
            $finder  = (new Finder())
                ->in($this->rootDir)
                ->ignoreDotFiles(false)
                ->directories();

            $paths = array_merge($paths, array_keys(iterator_to_array($finder)));
        }

        $this->inotify = inotify_init();

        foreach (array_filter($paths) as $path) {
            $this->addWatch($path);
        }

        $pid = Context::getPid();

        Event::add($this->inotify, function ($fd) use ($pid) {
            Context::attach($pid);

            $events = inotify_read($fd);

            $changes = [];

            foreach ($events as $event) {
                $base = $this->descriptors[$event['wd']] ?? null;
                if ($base) {
                    if ($event['mask'] & (IN_CREATE | IN_ISDIR)) {
                        $this->addWatch(Path::join($base, $event['name']));
                    }
                    if ($event['mask'] & IN_DELETE_SELF) {
                        $this->removeWatch($event['wd']);
                    }
                    if ($event['mask'] & (IN_CLOSE_WRITE | IN_DELETE | IN_MOVE)) {
                        $changes[] = Path::join($base, $event['name']);
                    }
                }
            }

            $this->emitChanges($changes);
        });
    }

    protected function removeWatch($wd)
    {
        if (isset($this->descriptors[$wd])) {
            @inotify_rm_watch($this->inotify, $wd);
            unset($this->descriptors[$wd]);
        }
    }

    protected function addWatch($path)
    {
        if (is_dir($path)) {
            $wd = inotify_add_watch($this->inotify, $path, IN_CLOSE_WRITE | IN_CREATE | IN_DELETE | IN_DELETE_SELF | IN_MOVE);

            $this->descriptors[$wd] = $path;
        }
    }

    protected function emitChanges($changes)
    {
        $this->changes = array_merge($this->changes, $changes);

        Timer::clear($this->changeTimer);

        $pid = Context::getPid();

        $this->changeTimer = Timer::after(100, function () use ($pid) {
            Context::attach($pid);
            if (!empty($this->changes)) {
                $changes = array_filter(array_map(function ($path) {
                    return $this->resolveRelativePath($path);
                }, array_values(array_unique($this->changes))));

                $this->changes = [];

                if (!empty($changes)) {
                    $this->emit('file.change', $changes);
                }
            }
            $this->updateStatus();
        });
    }

    protected function updateStatus()
    {
        try {
            $status = $this->repo->getStatus();

            $diff = new MapDiffer();

            $emitChange = !empty($diff->doDiff($this->status->files, $status->files))
                || !empty($diff->doDiff($this->status->branch, $status->branch));

            $this->status = $status;
            if ($emitChange) {
                $this->emit('status.change', $this->getStatus());
            }
        } catch (Exception $e) {
            if (!str_contains($e->getMessage(), 'Another git process seems to be running in this repository')) {
                Log::error($e->getMessage());
            }
        }
    }

    public function close()
    {
        Timer::clear($this->changeTimer);
        if ($this->inotify) {
            foreach ($this->descriptors as $wd => $path) {
                inotify_rm_watch($this->inotify, $wd);
            }
            $this->descriptors = [];
            Event::del($this->inotify);
            fclose($this->inotify);
            $this->inotify = null;
        }
    }

    protected function withLock(Closure $callable)
    {
        $time = time();
        while ($time + 30 > time() && $this->locked) {
            // 存在锁定则等待
            sleep(1);
        }
        try {
            $this->locked = true;
            return $callable();
        } finally {
            $this->updateStatus();
            $this->locked = false;
        }
    }

    protected function resolveRelativePath(string $path)
    {
        if ($path === $this->resolveFullPath(Config::FILE)) {
            //book.json文件不受root设置影响
            return Config::FILE;
        } else {
            return Path::makeRelative($path, $this->rootDir);
        }
    }

    protected function resolveLocalPath($path)
    {
        if (is_array($path)) {
            return array_map(function ($p) {
                return $this->resolveLocalPath($p);
            }, $path);
        }

        if ($path == Config::FILE) {
            return $this->resolveFullPath($path);
        }

        $path = Path::join($this->rootDir, $path);
        if (!Path::isBasePath($this->rootDir, $path)) {
            throw new FileScopeException("File out of scope: [{$path}]");
        }
        return $path;
    }

    protected function resolveFullPath(string $path)
    {
        $path = Path::join($this->workingDir, $path);
        if (!Path::isBasePath($this->workingDir, $path)) {
            throw new FileScopeException("File out of scope: [{$path}]");
        }
        return $path;
    }

    public function __destruct()
    {
        $this->close();
    }

}
