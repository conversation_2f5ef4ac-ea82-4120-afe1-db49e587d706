<?php

namespace app\repo\tools;

use app\repo\Workspace;
use think\agent\tool\Args;
use think\agent\tool\FunctionCall;
use think\agent\tool\result\Plain;
use think\agent\tool\result\Error;

class Insert extends FunctionCall
{
    protected $title = 'Insert text';

    protected $name = 'insert';

    protected $description = '在文件的指定位置插入新文本。';

    protected $parameters = [
        'path' => [
            'type'        => 'string',
            'description' => '要修改的文件路径',
            'required'    => true,
        ],
        'line' => [
            'type'        => 'integer',
            'description' => '插入位置的行号，0表示在文件开头插入，其他数字表示在该行后插入',
            'required'    => true,
        ],
        'str'  => [
            'type'        => 'string',
            'description' => '要插入的文本内容',
            'required'    => true,
        ],
    ];

    public function __construct(protected Workspace $workspace)
    {
    }

    protected function run(Args $args)
    {
        $path = $args->get('path');
        $line = $args->get('line');
        $str  = $args->get('str');

        // 读取文件内容
        $content = $this->workspace->readFile($path, true);

        $lines = explode("\n", $content);

        // 验证插入位置
        if ($line < 0 || $line > count($lines)) {
            return new Error("插入位置无效: {$line}，文件共有 " . count($lines) . " 行");
        }

        // 在指定位置插入新内容
        if ($line === 0) {
            // 在文件开头插入
            array_unshift($lines, $str);
        } else {
            // 在指定行后插入
            array_splice($lines, $line, 0, [$str]);
        }

        // 重新组合内容
        $newContent = implode("\n", $lines);

        // 写入文件
        $this->workspace->writeFile($path, $newContent);

        return new Plain("成功在 {$path} 的第 {$line} 行后插入了内容");
    }
}
