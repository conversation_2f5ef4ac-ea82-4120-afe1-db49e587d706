<?php

namespace app\repo\tools;

use app\repo\Workspace;
use think\agent\tool\Args;
use think\agent\tool\FunctionCall;
use think\agent\tool\result\Plain;
use think\agent\tool\result\Error;

class Create extends FunctionCall
{
    protected $title = 'Create file';

    protected $name = 'create';

    protected $description = '创建新文件并写入指定内容。';

    protected $parameters = [
        'path'    => [
            'type'        => 'string',
            'description' => '要创建的文件路径',
            'required'    => true,
        ],
        'content' => [
            'type'        => 'string',
            'description' => '要写入文件的内容',
            'default'     => '',
        ],
    ];

    public function __construct(protected Workspace $workspace)
    {
    }

    protected function run(Args $args)
    {
        $path    = $args->get('path');
        $content = $args->get('content', '');

        // 检查文件是否已存在
        if ($this->workspace->hasFile($path)) {
            return new Error("文件已存在: {$path}");
        }

        // 创建文件
        $this->workspace->writeFile($path, $content);

        return new Plain("文件创建成功: {$path}");
    }
}
