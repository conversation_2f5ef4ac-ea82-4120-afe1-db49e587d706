<?php

namespace app\repo\tools;

use app\repo\Workspace;
use think\agent\tool\Args;
use think\agent\tool\FunctionCall;
use think\agent\tool\result\Plain;

class Rename extends FunctionCall
{
    protected $title       = 'Rename file';
    protected $name        = 'rename';
    protected $description = '重命名文件或移动文件到新路径。';
    protected $parameters  = [
        'old_path' => [
            'type'        => 'string',
            'description' => '要重命名的文件或目录的旧路径',
            'required'    => true,
        ],
        'new_path' => [
            'type'        => 'string',
            'description' => '新的文件或目录路径',
            'required'    => true,
        ],
    ];

    public function __construct(protected Workspace $workspace)
    {
    }

    protected function run(Args $args)
    {
        $oldPath = $args->get('old_path');
        $newPath = $args->get('new_path');

        $this->workspace->renameFile($oldPath, $newPath);

        return new Plain("文件重命名成功: {$oldPath} -> {$newPath}");
    }

}
