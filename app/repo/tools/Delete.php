<?php

namespace app\repo\tools;

use app\repo\Workspace;
use think\agent\tool\Args;
use think\agent\tool\FunctionCall;
use think\agent\tool\result\Plain;

class Delete extends FunctionCall
{
    protected $title       = 'Delete file';
    protected $name        = 'delete';
    protected $description = '删除文件或目录。';
    protected $parameters  = [
        'path' => [
            'type'        => 'string',
            'description' => '要删除的文件或目录路径',
            'required'    => true,
        ],
    ];

    public function __construct(protected Workspace $workspace)
    {
    }

    protected function run(Args $args)
    {
        $path = $args->get('path');

        $this->workspace->removeFile($path);

        return new Plain("删除成功: {$path}");
    }

}
