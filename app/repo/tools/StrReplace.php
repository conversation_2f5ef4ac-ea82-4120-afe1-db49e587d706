<?php

namespace app\repo\tools;

use app\repo\Workspace;
use think\agent\tool\Args;
use think\agent\tool\FunctionCall;
use think\agent\tool\result\Plain;
use think\agent\tool\result\Error;

class StrReplace extends FunctionCall
{
    protected $title = 'Edit file';

    protected $name = 'str_replace';

    protected $description = '用新字符串替换文件中的特定字符串。这用于进行精确编辑。';

    protected $parameters = [
        'path'    => [
            'type'        => 'string',
            'description' => '要修改的文件路径',
            'required'    => true,
        ],
        'old_str' => [
            'type'        => 'string',
            'description' => '要被替换的原始文本，必须完全匹配包括空白字符和缩进',
            'required'    => true,
        ],
        'new_str' => [
            'type'        => 'string',
            'description' => '用来替换的新文本',
            'required'    => true,
        ],
    ];

    public function __construct(protected Workspace $workspace)
    {
    }

    protected function run(Args $args)
    {
        $path   = $args->get('path');
        $oldStr = $args->get('old_str');
        $newStr = $args->get('new_str');

        if (empty($oldStr)) {
            return new Error("要替换的文本不能为空");
        }

        // 读取文件内容
        $content = $this->workspace->readFile($path, true);

        // 检查要替换的文本是否存在
        $matchCount = substr_count($content, $oldStr);

        if ($matchCount === 0) {
            return new Error("未找到要替换的文本");
        }

        if ($matchCount > 1) {
            return new Error("找到 {$matchCount} 个匹配项，请提供更具体的文本以确保唯一匹配");
        }

        // 执行替换
        $newContent = str_replace($oldStr, $newStr, $content);

        // 写入文件
        $this->workspace->writeFile($path, $newContent);

        return new Plain("成功在 {$path} 中替换了 1 处文本");
    }
}
