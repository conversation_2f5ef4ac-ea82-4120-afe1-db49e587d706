<?php

namespace app\repo\tools;

use app\repo\Workspace;
use think\agent\tool\Args;
use think\agent\tool\FunctionCall;
use think\agent\tool\result\Json;
use think\agent\tool\result\Plain;
use think\agent\tool\result\Error;

class View extends FunctionCall
{
    protected $title = 'Read file';

    protected $name = 'view';

    protected $description = '查看文件内容或目录列表。可以查看整个文件或指定行范围的内容。';

    protected $parameters = [
        'path'  => [
            'type'        => 'string',
            'description' => '要查看的文件或目录路径',
            'required'    => true,
        ],
        'range' => [
            'type'        => 'array',
            'description' => '可选，指定要查看的行范围 [开始行, 结束行]，-1表示到文件末尾',
            'items'       => [
                'type' => 'integer',
            ],
        ],
    ];

    public function __construct(protected Workspace $workspace)
    {
    }

    protected function run(Args $args)
    {
        $path = $args->get('path');

        // 先尝试作为文件处理
        if ($this->workspace->hasFile($path)) {
            $range = $args->get('range', false);
            return $this->viewFile($path, $range);
        } elseif ($this->workspace->hasDir($path)) {
            return $this->viewDirectory($path);
        } else {
            return new Error("路径不存在: {$path}");
        }
    }

    private function viewDirectory($path)
    {
        $files = $this->workspace->readDir($path);

        return new Json($files);
    }

    private function viewFile($path, $viewRange = false)
    {
        $content = $this->workspace->readFile($path, true);

        // 如果指定了行范围，则只返回指定范围的内容
        if ($viewRange && is_array($viewRange) && count($viewRange) === 2) {
            $lines     = explode("\n", $content);
            $startLine = max(1, (int) $viewRange[0]) - 1; // 转换为0基索引
            $endLine   = (int) $viewRange[1];

            if ($endLine === -1) {
                $endLine = count($lines);
            } else {
                $endLine = min($endLine, count($lines));
            }

            $selectedLines = array_slice($lines, $startLine, $endLine - $startLine);
            $content       = implode("\n", $selectedLines);
        }

        return new Plain($content);
    }
}
