<?php

namespace app\controller\space\assistant;

use app\controller\space\Controller;
use app\lib\Date;
use function think\swoole\helper\iterator;

class IndexController extends Controller
{
    public function index()
    {
        $conversation = $this->space->conversations()
            ->with(['messages'])
            ->order('update_time desc')
            ->where('update_time', '>', Date::now()->subHours(12))
            ->find();

        return [
            'conversation' => $conversation,
        ];
    }

    public function chat()
    {
        $params = $this->validate([
            'input'        => 'require',
            'conversation' => '',
            'token'        => '',
        ]);

        $agent = $this->space->getAgent($this->getWorkspace());

        $result = $agent->run($params);

        $result->rewind();

        $generator = function () use ($agent, $result) {
            foreach ($result as $data) {
                $connected = yield 'data: ' . json_encode($data) . "\n\n";
                if (!$connected) {
                    $agent->stop();
                    break;
                }
            }
            yield "data: [DONE]\n\n";
        };

        $response = iterator($generator());

        return $response->header([
            'Content-Type'      => 'text/event-stream',
            'Cache-Control'     => 'no-cache, must-revalidate',
            'X-Accel-Buffering' => 'no',
        ]);
    }

}
