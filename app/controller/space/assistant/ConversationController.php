<?php

namespace app\controller\space\assistant;

use app\controller\space\Controller;

class ConversationController extends Controller
{
    public function index()
    {
        $conversations = $this->space->conversations()
            ->with(['messages'])
            ->order('update_time desc')
            ->paginate();

        return json($conversations);
    }

    public function delete($id)
    {
        $this->space->conversations()->findOrFail($id)->delete();
    }
}
